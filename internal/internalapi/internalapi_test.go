// Copyright Envoy AI Gateway Authors
// SPDX-License-Identifier: Apache-2.0
// The full text of the Apache license is available in the LICENSE file at
// the root of the repo.

package internalapi

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestPerRouteRuleRefBackendName(t *testing.T) {
	tests := []struct {
		name           string
		namespace      string
		backendName    string
		routeName      string
		routeRuleIndex int
		refIndex       int
		expected       string
	}{
		{
			name:           "basic case",
			namespace:      "default",
			backendName:    "backend1",
			routeName:      "route1",
			routeRuleIndex: 0,
			refIndex:       0,
			expected:       "default/backend1/route/route1/rule/0/ref/0",
		},
		{
			name:           "different namespace",
			namespace:      "test-ns",
			backendName:    "my-backend",
			routeName:      "my-route",
			routeRuleIndex: 2,
			refIndex:       1,
			expected:       "test-ns/my-backend/route/my-route/rule/2/ref/1",
		},
		{
			name:           "with special characters",
			namespace:      "ns-with-dash",
			backendName:    "backend_with_underscore",
			routeName:      "route-with-dash",
			routeRuleIndex: 10,
			refIndex:       5,
			expected:       "ns-with-dash/backend_with_underscore/route/route-with-dash/rule/10/ref/5",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := PerRouteRuleRefBackendName(tt.namespace, tt.backendName, tt.routeName, tt.routeRuleIndex, tt.refIndex)
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestClusterRefInferencePool(t *testing.T) {
	tests := []struct {
		name        string
		namespace   string
		poolName    string
		serviceName string
		servicePort uint32
		expected    string
	}{
		{
			name:        "basic case",
			namespace:   "default",
			poolName:    "pool1",
			serviceName: "service1",
			servicePort: 8080,
			expected:    "default/pool1/service1/8080",
		},
		{
			name:        "different port",
			namespace:   "test-ns",
			poolName:    "my-pool",
			serviceName: "my-service",
			servicePort: 9090,
			expected:    "test-ns/my-pool/my-service/9090",
		},
		{
			name:        "with special characters",
			namespace:   "ns-with-dash",
			poolName:    "pool_with_underscore",
			serviceName: "service-with-dash",
			servicePort: 443,
			expected:    "ns-with-dash/pool_with_underscore/service-with-dash/443",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ClusterRefInferencePool(tt.namespace, tt.poolName, tt.serviceName, tt.servicePort)
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestConstants(t *testing.T) {
	// Test that constants have expected values
	require.Equal(t, "aigateway.envoy.io", InternalEndpointMetadataNamespace)
	require.Equal(t, "per_route_rule_backend_name", InternalMetadataBackendNameKey)
}
